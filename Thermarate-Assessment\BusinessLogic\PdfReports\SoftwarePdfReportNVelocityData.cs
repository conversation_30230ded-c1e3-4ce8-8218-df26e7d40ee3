﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using RediSoftware.Dtos;
using RediSoftware.Helpers;
using System.Web.Hosting;
using AutoMapper;
using RediSoftware.Services;
using TenureExtraction;


namespace RediSoftware.BusinessLogic
{
    // TODO: Inherit from base class (when it exists?)
    public class SoftwarePdfReportNVelocityData
    {
        private Guid _optionId;
        public BuildingType? BuildingType;

        private ExtendedAssessmentDto _assessment;
        private AssessmentComplianceOptionDto _option;
        private EnergyPlusWeatherHeaderDataDto _weatherHeader;

        private IUnitOfWork _unitOfWork;
        private IMapper _mapper;
        private EnergyPlusWeatherData _energyPlusWeatherData;

        // This is all data filled in during the "Initialize" function.
        public List<object> BuildingAreaRows { get; set; } = new List<object>();
        public List<ZoneSummaryRow> ZoneSummaryRows { get; set; } = new List<ZoneSummaryRow>();
        public List<object> WindowWallRatioRows { get; set; } = new List<object>();
        public List<object> ConditionedWindowWallRatioRows { get; set; } = new List<object>();
        public List<object> SkylightRoofRatioRows { get; set; } = new List<object>();
        public List<ConstructionRow> OpaqueExteriorRows { get; set; } = new List<ConstructionRow>();
        public List<ConstructionRow> OpaqueInteriorRows { get; set; } = new List<ConstructionRow>();
        public List<ConstructionRow> ExteriorFenestrationRows { get; set; } = new List<ConstructionRow>();
        public List<ConstructionRow> ExteriorDoorRows { get; set; } = new List<ConstructionRow>();
        public List<ConstructionRow> InteriorDoorRows { get; set; } = new List<ConstructionRow>();

        public List<object> EndUsesRows { get; set; } = new List<object>();
        public List<object> UtilityUsePerConditionedFloorAreaRows { get; set; } = new List<object>();
        public List<object> UtilityUsePerTotalFloorAreaRows { get; set; } = new List<object>();

        public List<object> MonthlyHeatingSummaryRows { get; set; } = new List<object>();
        public List<object> MonthlyCoolingSummaryRows { get; set; } = new List<object>();


        public SoftwarePdfReportNVelocityData(
            IUnitOfWork unitOfWork,
            IMapper mapper,
            EnergyPlusWeatherData energyPlusWeatherData)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _energyPlusWeatherData = energyPlusWeatherData;

        }

        public async Task Initialize(Guid optionId)
        {
            try
            {
                if (BuildingType == null)
                    throw new Exception("BuildingType must be set before calling Initialize.");

                _optionId = optionId;

                var optionDb = _unitOfWork.Context.RSS_AssessmentComplianceOption
                    .First(x => x.ComplianceOptionsId == _optionId);

                _option = _mapper.Map<AssessmentComplianceOptionDto>(optionDb);

                var assessmentDb = _unitOfWork.Context.RSS_Assessment
                    .First(x => x.AssessmentId == _option.AssessmentId);

                _assessment = Assessment.BuildExtendedDto(assessmentDb, _unitOfWork, _mapper);

                _weatherHeader = await _energyPlusWeatherData
                    .GetMetadataForClimateZone(Convert.ToInt16(_assessment.NatHERSClimateZone.Description));


                this.BuildingAreaRows = CalculateBuildingAreaRows();
                this.ZoneSummaryRows = CalculateZoneSummaryRows(this.Building);
                this.WindowWallRatioRows = CalculateWindowWallRatioRows(this.Building, "all");
                this.ConditionedWindowWallRatioRows = CalculateWindowWallRatioRows(this.Building, "yes");
                this.SkylightRoofRatioRows = CalculateSkylightRoofRatioRows(this.Building);

                this.OpaqueExteriorRows = CalculateOpaqueExteriorRows(this.Building);
                this.OpaqueInteriorRows = CalculateOpaqueInteriorRows(this.Building);
                this.ExteriorFenestrationRows = CalculateExteriorFenestrationRows(this.Building);
                this.ExteriorDoorRows = CalculateExteriorDoorRows(this.Building);
                this.InteriorDoorRows = CalculateInteriorDoorRows(this.Building);

                this.EndUsesRows = CalculateEndUsesRows(this.Building);
                this.UtilityUsePerConditionedFloorAreaRows = CalculateUtilityUsePerConditionedFloorAreaRows(this.Building);
                this.UtilityUsePerTotalFloorAreaRows = CalculateUtilityUsePerTotalFloorAreaRows(this.Building);

                this.MonthlyHeatingSummaryRows = CalculateMonthlyHeatingSummaryRows(this.Building);
                this.MonthlyCoolingSummaryRows = CalculateMonthlyCoolingSummaryRows(this.Building);
            }
            catch (Exception e)
            {
                ;
                throw;
            }


        }

        private List<object> CalculateMonthlyHeatingSummaryRows(AssessmentComplianceBuildingDto building)
        {
            // Alrighty so we actually need to return a row for each zone. Any zone not in the energy output still gets
            // shown with a value of 0 for all months.

            var rows = new List<object>();
            foreach (var zone in building.Zones)
            {

                if (!Zone.IsInterior(zone))
                    continue;

                StoreyDto storey;
                if (zone.Storey.HasValue == false)
                    storey = new StoreyDto() { Name = "Ground Floor" };
                else
                    storey = Building.Storeys[zone.Storey.Value];

                string key = zone.ZoneNumber.ToLower();
                bool exists = building.EnergyUsageSummary.PerZoneMonthlyHeating.ContainsKey(key);

                var row = new
                {
                    Title = $"{storey.Name}:{zone.ZoneDescription}".ToUpper(),
                    Jan =  !exists ? 0 : building.EnergyUsageSummary.PerZoneMonthlyHeating[key]?[0] ?? 0,
                    Feb =  !exists ? 0 : building.EnergyUsageSummary.PerZoneMonthlyHeating[key]?[1] ?? 0,
                    Mar =  !exists ? 0 : building.EnergyUsageSummary.PerZoneMonthlyHeating[key]?[2] ?? 0,
                    Apr =  !exists ? 0 : building.EnergyUsageSummary.PerZoneMonthlyHeating[key]?[3] ?? 0,
                    May =  !exists ? 0 : building.EnergyUsageSummary.PerZoneMonthlyHeating[key]?[4] ?? 0,
                    Jun =  !exists ? 0 : building.EnergyUsageSummary.PerZoneMonthlyHeating[key]?[5] ?? 0,
                    Jul =  !exists ? 0 : building.EnergyUsageSummary.PerZoneMonthlyHeating[key]?[6] ?? 0,
                    Aug =  !exists ? 0 : building.EnergyUsageSummary.PerZoneMonthlyHeating[key]?[7] ?? 0,
                    Sep =  !exists ? 0 : building.EnergyUsageSummary.PerZoneMonthlyHeating[key]?[8] ?? 0,
                    Oct =  !exists ? 0 : building.EnergyUsageSummary.PerZoneMonthlyHeating[key]?[9] ?? 0,
                    Nov =  !exists ? 0 : building.EnergyUsageSummary.PerZoneMonthlyHeating[key]?[10] ?? 0,
                    Dec =  !exists ? 0 : building.EnergyUsageSummary.PerZoneMonthlyHeating[key]?[11] ?? 0,
                    Total = !exists ? 0 :building.EnergyUsageSummary.PerZoneAnnualHeating[key],
                };

                rows.Add(row);
            }

            return rows;
        }

        private List<object> CalculateMonthlyCoolingSummaryRows(AssessmentComplianceBuildingDto building)
        {
            // Alrighty so we actually need to return a row for each zone. Any zone not in the energy output still gets
            // shown with a value of 0 for all months.

            var rows = new List<object>();
            foreach (var zone in building.Zones)
            {
                if (!Zone.IsInterior(zone))
                    continue;

                StoreyDto storey;
                if (zone.Storey.HasValue == false)
                    storey = new StoreyDto() { Name = "Ground Floor" };
                else
                    storey = Building.Storeys[zone.Storey.Value];

                string key = zone.ZoneNumber.ToLower();
                bool exists = building.EnergyUsageSummary.PerZoneMonthlyCooling.ContainsKey(key);

                var row = new
                {
                    Title = $"{storey.Name}:{zone.ZoneDescription}".ToUpper(),
                    Jan =  !exists ? 0 : building.EnergyUsageSummary.PerZoneMonthlyCooling[key]?[0] ?? 0,
                    Feb =  !exists ? 0 : building.EnergyUsageSummary.PerZoneMonthlyCooling[key]?[1] ?? 0,
                    Mar =  !exists ? 0 : building.EnergyUsageSummary.PerZoneMonthlyCooling[key]?[2] ?? 0,
                    Apr =  !exists ? 0 : building.EnergyUsageSummary.PerZoneMonthlyCooling[key]?[3] ?? 0,
                    May =  !exists ? 0 : building.EnergyUsageSummary.PerZoneMonthlyCooling[key]?[4] ?? 0,
                    Jun =  !exists ? 0 : building.EnergyUsageSummary.PerZoneMonthlyCooling[key]?[5] ?? 0,
                    Jul =  !exists ? 0 : building.EnergyUsageSummary.PerZoneMonthlyCooling[key]?[6] ?? 0,
                    Aug =  !exists ? 0 : building.EnergyUsageSummary.PerZoneMonthlyCooling[key]?[7] ?? 0,
                    Sep =  !exists ? 0 : building.EnergyUsageSummary.PerZoneMonthlyCooling[key]?[8] ?? 0,
                    Oct =  !exists ? 0 : building.EnergyUsageSummary.PerZoneMonthlyCooling[key]?[9] ?? 0,
                    Nov =  !exists ? 0 : building.EnergyUsageSummary.PerZoneMonthlyCooling[key]?[10] ?? 0,
                    Dec =  !exists ? 0 : building.EnergyUsageSummary.PerZoneMonthlyCooling[key]?[11] ?? 0,
                    Total = !exists ? 0 :building.EnergyUsageSummary.PerZoneAnnualCooling[key],
                };

                rows.Add(row);
            }

            return rows;
        }

        private List<object> CalculateEndUsesRows(AssessmentComplianceBuildingDto building)
        {
            decimal heating = building.EnergyUsageSummary?.YearlyHeating ?? 0;
            decimal cooling = building.EnergyUsageSummary?.YearlyCooling ?? 0;
            decimal fans = building.EnergyUsageSummary?.OutputSummaryData?.CeilingFanEnergyUse ?? 0;

            return new List<object>
            {
                new { Title = "Heating", Electricity = heating },
                new { Title = "Cooling", Electricity = cooling },
                new { Title = "Fans", Electricity = fans },
                new { Title = "Total", Electricity = heating + cooling + fans }
            };
        }

        private List<object> CalculateUtilityUsePerConditionedFloorAreaRows(AssessmentComplianceBuildingDto building)
        {
            return new List<object>
            {
                new
                {
                    Title = "HVAC",
                    building.Heating,
                    building.Cooling,
                    Total = building.Heating + building.Cooling
                },
            };
        }

        private List<object> CalculateUtilityUsePerTotalFloorAreaRows(AssessmentComplianceBuildingDto building)
        {
            var heating = (building.Heating * building.ConditionedFloorArea) / ( building.ConditionedFloorArea + building.UnconditionedFloorArea);
            var cooling = (building.Cooling * building.ConditionedFloorArea) / ( building.ConditionedFloorArea + building.UnconditionedFloorArea);
            return new List<object>
            {
                new
                {
                    Title = "HVAC",
                    Heating = heating,
                    Cooling = cooling,
                    Total = heating + cooling
                },
            };
        }

        public AssessmentComplianceOptionDto Simulation => _assessment.SelectedSimulation;

        public AssessmentComplianceBuildingDto Building => BuildingType == BusinessLogic.BuildingType.Proposed
            ? Simulation.Proposed
            : Simulation.Reference;

        public string SoftwareReportStyleSheetLocation
        {
            get
            {
                string directory = HostingEnvironment.MapPath("/Templates");
                return directory + "/software-report-template-styles.css";
            }
        }

        public string ProgramVersion =>
            $"{Simulation.AssessmentSoftware.Description}, Version {Simulation.AssessmentSoftware.SoftwareVersion}";

        public string BuildingTypeDescription =>
            BuildingType == BusinessLogic.BuildingType.Proposed
                ? "Proposed Building"
                : Simulation.ComplianceMethodCode == "CMPerfSolution"
                    ? "Reference Building"
                    : "DTS Building";

        public string Environment
        {
            get
            {
                // Mandurah WA AUS NatHERS-TMY2-A BoM 9977 CZ0508 WMO#=946050
                var h = _weatherHeader;
                return $"{h.CityA1} {h.StateProvinceRegionA2} {h.CountryA3} {h.DataSourceA4} WMO#={h.WmoNumberN1}";
            }
        }

        public string SimulationTimestamp => _assessment.CerficateDate?.ToString("yyyy.MM.dd hh:mm") ?? "";

        public string Latitude => _weatherHeader.LatitudeN2.ToString("F2");
        public string Longitude => _weatherHeader.LongitudeN3.ToString("F2");
        public string Elevation => _weatherHeader.ElevationN5.ToString("F2");
        public string TimeZone => _weatherHeader.TimeZoneN4.ToString("F2");
        public string NorthAxisAngle => "0.00";

        public string SoftwareUnits => Simulation.AssessmentSoftware?.EnergyLoadUnits ?? "?";


        public List<object> CalculateBuildingAreaRows()
        {
            var totalBuildingArea = (Building.ConditionedFloorArea.Value + Building.UnconditionedFloorArea.Value).ToString("F2");

            return new List<object>
            {
                new { Title = "Total Building Area", Area = totalBuildingArea },
                new { Title = "Net Conditioned Building Area", Area = Building.ConditionedFloorArea.Value.ToString("F2") },
                new { Title = "Unconditioned Building Area", Area = Building.UnconditionedFloorArea.Value.ToString("F2") }
            };
        }

        public List<ZoneSummaryRow> CalculateZoneSummaryRows(AssessmentComplianceBuildingDto building)
        {
            // Storeyname:zonename

            var zoneSummaries = new List<ZoneSummaryRow>();

            var allExteriorWalls = ExteriorWalls(building);
            var allExteriorGlazing = ExteriorGlazing(building);

            foreach (var zone in building.Zones)
            {

                StoreyDto storey;

                // TODO: Ground Floor when unknown? Does this make sense?
                if (zone.ZoneActivity.ZoneActivityCode == "ZASubfloorSpace")
                    storey = Building.Storeys[zone.StoreyAbove.Value];
                else if (zone.ZoneActivity.ZoneActivityCode == "ZARoofSpace")
                    storey = Building.Storeys[zone.StoreyBelow.Value];
                else if (Zone.IsInterior(zone))
                    storey = Building.Storeys[zone.Storey.Value];
                else
                    continue;

                var waw = ComputeWallAndWindowAreas(zone, allExteriorWalls, allExteriorGlazing);

                var row = new ZoneSummaryRow()
                {
                    Title = $"{storey.Name}:{zone.ZoneDescription}".ToUpper(),
                    Conditioned = Zone.IsInterior(zone)
                        ? zone.Conditioned.HasValue && zone.Conditioned.Value
                            ? "Yes"
                            : "No"
                        : "No",
                    PartOfTotalFloorArea = Zone.IsInterior(zone) ? "Yes" : "No",
                    FloorArea = zone.FloorArea.HasValue ? zone.FloorArea.Value : 0.00m,
                    Volume = zone.Volume.HasValue ? zone.Volume.Value : 0.00m,

                    GrossWallArea = waw.WallArea,
                    WindowArea = waw.WindowArea,
                    Floor = storey.Floor,
                };

                zoneSummaries.Add(row);
            }

            zoneSummaries = zoneSummaries
                .OrderByDescending(x => x.PartOfTotalFloorArea)
                .ThenBy(x => x.Floor)
                .ThenBy(x => x.Title)
                .ToList();

            var totalList = zoneSummaries.Where(x => x.PartOfTotalFloorArea == "Yes");
            var total = new ZoneSummaryRow()
            {
                Title = "Total",
                Conditioned = "",
                PartOfTotalFloorArea = "",
                FloorArea = totalList.Count() == 0 ? 0 : totalList.Select(x => x.FloorArea).Aggregate((a, b) => a + b),
                Volume = totalList.Count() == 0 ? 0 : totalList.Select(x => x.Volume).Aggregate((a, b) => a + b),
                GrossWallArea = totalList.Count() == 0 ? 0 : totalList.Select(x => x.GrossWallArea).Aggregate((a, b) => a + b),
                WindowArea = totalList.Count() == 0 ? 0 : totalList.Select(x => x.WindowArea).Aggregate((a, b) => a + b),

            };

            var contitionedList = zoneSummaries.Where(x => x.PartOfTotalFloorArea == "Yes" && x.Conditioned == "Yes");
            var conditionedTotal = new ZoneSummaryRow()
            {
                Title = "Conditioned Total",
                Conditioned = "",
                PartOfTotalFloorArea = "",
                FloorArea = contitionedList.Count() == 0 ? 0 : contitionedList.Select(x => x.FloorArea).Aggregate((a, b) => a + b),
                Volume = contitionedList.Count() == 0 ? 0 : contitionedList.Select(x => x.Volume).Aggregate((a, b) => a + b),
                GrossWallArea = contitionedList.Count() == 0 ? 0 : contitionedList.Select(x => x.GrossWallArea).Aggregate((a, b) => a + b),
                WindowArea = contitionedList.Count() == 0 ? 0 : contitionedList.Select(x => x.WindowArea).Aggregate((a, b) => a + b),
            };

            var unconditionedList = zoneSummaries.Where(x => x.PartOfTotalFloorArea == "Yes" && x.Conditioned == "No");
            var unconditionedTotal = new ZoneSummaryRow()
            {
                Title = "Unconditioned Total",
                Conditioned = "",
                PartOfTotalFloorArea = "",
                FloorArea = unconditionedList.Count() == 0 ? 0 : unconditionedList.Select(x => x.FloorArea).Aggregate((a, b) => a + b),
                Volume = unconditionedList.Count() == 0 ? 0 : unconditionedList.Select(x => x.Volume).Aggregate((a, b) => a + b),
                GrossWallArea = unconditionedList.Count() == 0 ? 0 : unconditionedList.Select(x => x.GrossWallArea).Aggregate((a, b) => a + b),
                WindowArea = unconditionedList.Count() == 0 ? 0 : unconditionedList.Select(x => x.WindowArea).Aggregate((a, b) => a + b),

            };

            var notPartOfTotalList = zoneSummaries.Where(x => x.PartOfTotalFloorArea == "No");
            var notPartOfTotal = new ZoneSummaryRow()
            {
                Title = "Not Part of Total",
                Conditioned = "",
                PartOfTotalFloorArea = "",
                FloorArea = notPartOfTotalList.Count() == 0 ? 0 : notPartOfTotalList.Select(x => x.FloorArea).Aggregate((a, b) => a + b),
                Volume = notPartOfTotalList.Count() == 0 ? 0 : notPartOfTotalList.Select(x => x.Volume).Aggregate((a, b) => a + b),
                GrossWallArea = notPartOfTotalList.Count() == 0 ? 0 : notPartOfTotalList.Select(x => x.GrossWallArea).Aggregate((a, b) => a + b),
                WindowArea = notPartOfTotalList.Count() == 0 ? 0 : notPartOfTotalList.Select(x => x.WindowArea).Aggregate((a, b) => a + b),
            };

            zoneSummaries.Add(total);
            zoneSummaries.Add(conditionedTotal);
            zoneSummaries.Add(unconditionedTotal);
            zoneSummaries.Add(notPartOfTotal);

            return zoneSummaries;
        }

        private List<SurfaceTemplateDto> ExteriorWalls(AssessmentComplianceBuildingDto building)
        {
            var list = building.Surfaces?.Where(x => x.Category.ConstructionCategoryCode == "ExteriorWall").Select(x => x.Elements);
            return list.Count() == 0 ? new List<SurfaceTemplateDto>() : list.Aggregate(new List<SurfaceTemplateDto>(), (a, b) => a.Concat(b).ToList());
        }

        private List<OpeningTemplateDto> ExteriorGlazing(AssessmentComplianceBuildingDto building)
        {
            var list = building.Openings?.Where(x => x.Category.ConstructionCategoryCode == "ExteriorGlazing").Select(x => x.Elements);
            return list.Count() == 0 ? new List<OpeningTemplateDto>() : list.Aggregate(new List<OpeningTemplateDto>(), (a, b) => a.Concat(b).ToList());
        }

        public (decimal WallArea, decimal WindowArea) ComputeWallAndWindowAreas(
            ZoneDto zone,
            List<SurfaceTemplateDto> allExtWalls,
            List<OpeningTemplateDto> allExtGlazings) {

            var wallsInZone = allExtWalls.Where(x => x.ParentZoneId == zone.LinkId);
            var glazingsInZone = allExtGlazings.Where(x => x.ParentZoneId == zone.LinkId);

            var wallArea = wallsInZone.Select(x => x.GrossArea.HasValue ? x.GrossArea.Value : 0m).Aggregate(0m, (a, b) => a + b);
            var windowArea = glazingsInZone.Select(x => x.GrossArea.HasValue ? x.GrossArea.Value : 0m).Aggregate(0m, (a, b) => a + b);

            return (wallArea, windowArea);

        }

        public List<object> CalculateWindowWallRatioRows(AssessmentComplianceBuildingDto building, string conditioned)
        {
            List<object> rows = new List<object>();


            var wallArea = new XYRatioRow()
            {
                Title = "Gross Wall Area [m2]",
                North = CalculateWallAreaPerSector(building, building.Surfaces, conditioned, "N"),
                East = CalculateWallAreaPerSector(building, building.Surfaces, conditioned, "E"),
                South = CalculateWallAreaPerSector(building, building.Surfaces, conditioned, "S"),
                West = CalculateWallAreaPerSector(building, building.Surfaces, conditioned, "W"),
            };
            wallArea.Total = wallArea.North + wallArea.East + wallArea.South + wallArea.West;
            rows.Add(wallArea);

            // Add 'wall area' row.
            var windowArea = new XYRatioRow()
            {
                Title = "Window Area [m2]",
                North = CalculateWindowAreaPerSector(building, building.Openings, conditioned, "N"),
                East = CalculateWindowAreaPerSector(building, building.Openings, conditioned, "E"),
                South = CalculateWindowAreaPerSector(building, building.Openings, conditioned, "S"),
                West = CalculateWindowAreaPerSector(building, building.Openings, conditioned, "W"),
            };
            windowArea.Total = windowArea.North + windowArea.East + windowArea.South + windowArea.West;
            rows.Add(windowArea);

            // Add 'ratio' row
            var ratio = new XYRatioRow()
            {
                Title = "Gross Window-Wall Ratio [%]",
                Total = wallArea.Total == 0 ? 0 :  windowArea.Total / wallArea.Total * 100,
                North = wallArea.North == 0 ? 0 :  windowArea.North / wallArea.North * 100,
                East = wallArea.East == 0 ? 0 :  windowArea.East / wallArea.East * 100,
                South = wallArea.South == 0 ? 0 :  windowArea.South / wallArea.South * 100,
                West = wallArea.West == 0 ? 0 :  windowArea.West / wallArea.West * 100,
            };

            rows.Add(ratio);

            return rows;

        }

        // Returns the data for a single cell.
        public decimal CalculateWallAreaPerSector(
            AssessmentComplianceBuildingDto building,
            List<SurfaceTemplateDto> parents,
            string conditioned,
            string direction)
        {

            if (direction != "N" && direction != "E" && direction != "S" && direction != "W")
                throw new Exception("Invalid direction specified");

            if (conditioned != "all" && conditioned != "yes" && conditioned != "no")
                throw new Exception("Invalid value for 'conditioned' specified.");

            decimal area = 0;

            foreach (var parent in parents)
            {
                if (parent.Category.ConstructionCategoryCode != "ExteriorWall")
                    continue;

                foreach (var child in parent.Elements)
                {
                    var windowZone = building.Zones
                        .FirstOrDefault(x => x.LinkId == child.ParentZoneId);

                    if(windowZone == null)
                        continue;

                    bool conditionedPassed = false;
                    if (conditioned == "all")
                        conditionedPassed = true;
                    else if (conditioned == "yes")
                        conditionedPassed = windowZone.Conditioned.Value == true;
                    else
                        conditionedPassed = windowZone.Conditioned.Value == false;

                    if (conditionedPassed == false)
                        continue;

                    if(AzimuthToNeswString(child.Azimuth, child.Category.ConstructionCategoryCode) == direction)
                        area += child.GrossArea.HasValue ? child.GrossArea.Value : 0m;

                }

            }

            return area;
        }

        // Returns the data for a single cell.
        public decimal CalculateWindowAreaPerSector(
            AssessmentComplianceBuildingDto building,
            List<OpeningTemplateDto> parents,
            string conditioned,
            string direction)
        {

            if (direction != "N" && direction != "E" && direction != "S" && direction != "W")
                throw new Exception("Invalid direction specified");

            if (conditioned != "all" && conditioned != "yes" && conditioned != "no")
                throw new Exception("Invalid value for 'conditioned' specified.");

            decimal area = 0;

            foreach (var parent in parents)
            {
                if (parent.Category.ConstructionCategoryCode != "ExteriorGlazing")
                    continue;


                foreach (var child in parent.Elements)
                {
                    var windowZone = building.Zones
                        .FirstOrDefault(x => x.LinkId == child.ParentZoneId);

                    if(windowZone == null)
                        continue;

                    bool conditionedPassed = false;
                    if (conditioned == "all")
                        conditionedPassed = true;
                    else if (conditioned == "yes")
                        conditionedPassed = windowZone.Conditioned.Value == true;
                    else
                        conditionedPassed = windowZone.Conditioned.Value == false;

                    if (conditionedPassed == false)
                        continue;

                    if(AzimuthToNeswString(child.Azimuth, child.Category.ConstructionCategoryCode) == direction)
                        area += child.GrossArea.HasValue ? child.GrossArea.Value : 0m;

                }

            }

            return area;

        }

        public List<object> CalculateSkylightRoofRatioRows(AssessmentComplianceBuildingDto building)
        {
            List<object> rows = new List<object>();

            var roofArea = new XYRatioRow()
            {
                Title = "Gross Roof Area [m2]",
                North = CalculateRoofAreaPerSector(building, building.Surfaces, "N"),
                East = CalculateRoofAreaPerSector(building, building.Surfaces, "E"),
                South = CalculateRoofAreaPerSector(building, building.Surfaces, "S"),
                West = CalculateRoofAreaPerSector(building, building.Surfaces, "W"),
            };
            roofArea.Total = roofArea.North + roofArea.East + roofArea.South + roofArea.West;
            rows.Add(roofArea);

            // Add 'wall area' row.
            var skylightArea = new XYRatioRow()
            {
                Title = "Skylight Area [m2]",
                North = CalculateSkylightAreaPerSector(building, building.Openings, "N"),
                East = CalculateSkylightAreaPerSector(building, building.Openings, "E"),
                South = CalculateSkylightAreaPerSector(building, building.Openings, "S"),
                West = CalculateSkylightAreaPerSector(building, building.Openings, "W"),
            };
            skylightArea.Total = skylightArea.North + skylightArea.East + skylightArea.South + skylightArea.West;
            rows.Add(skylightArea);

            // Add 'ratio' row
            var ratio = new XYRatioRow()
            {
                Title = "Skylight-Roof Ratio [%]",
                Total = roofArea.Total == 0 ? 0 :  skylightArea.Total / roofArea.Total * 100,
                North = roofArea.North == 0 ? 0 :  skylightArea.North / roofArea.North * 100,
                East = roofArea.East == 0 ? 0 :  skylightArea.East / roofArea.East * 100,
                South = roofArea.South == 0 ? 0 :  skylightArea.South / roofArea.South * 100,
                West = roofArea.West == 0 ? 0 :  skylightArea.West / roofArea.West * 100,
            };

            rows.Add(ratio);

            return rows;

        }

        public decimal CalculateRoofAreaPerSector(
            AssessmentComplianceBuildingDto building,
            List<SurfaceTemplateDto> parents,
            string direction)
        {

            if (direction != "N" && direction != "E" && direction != "S" && direction != "W")
                throw new Exception("Invalid direction specified");

            decimal area = 0;

            foreach (var parent in parents)
            {
                if (parent.Category.ConstructionCategoryCode != "Roof")
                    continue;

                foreach (var child in parent.Elements)
                {
                    var windowZone = building.Zones
                        .FirstOrDefault(x => x.LinkId == child.ParentZoneId);

                    if(windowZone == null)
                        continue;

                    if(AzimuthToNeswString(child.Azimuth, child.Category.ConstructionCategoryCode) == direction)
                        area += child.GrossArea.HasValue ? child.GrossArea.Value : 0m;
                }
            }

            return area;

        }

        public decimal CalculateSkylightAreaPerSector(
            AssessmentComplianceBuildingDto building,
            List<OpeningTemplateDto> parents,
            string direction)
        {

            if (direction != "N" && direction != "E" && direction != "S" && direction != "W")
                throw new Exception("Invalid direction specified");

            decimal area = 0;

            foreach (var parent in parents)
            {
                if (parent.Category.ConstructionCategoryCode != "RoofWindow" && parent.Category.ConstructionCategoryCode != "Skylight")
                    continue;

                foreach (var child in parent.Elements)
                {
                    var windowZone = building.Zones
                        .FirstOrDefault(x => x.LinkId == child.ParentZoneId);

                    if(windowZone == null)
                        continue;

                    if(AzimuthToNeswString(child.Azimuth, child.Category.ConstructionCategoryCode) == direction)
                        area += child.GrossArea.HasValue ? child.GrossArea.Value : 0m;
                }
            }

            return area;

        }

        public List<ConstructionRow> GenerateConstructionRowsFromElements(
            AssessmentComplianceBuildingDto building, List<SurfaceTemplateDto> parents, bool calculateUValueIfZero = false)
        {
            var rows = new List<ConstructionRow>();
            foreach (var parent in parents)
            {
                foreach (var child in parent.Elements)
                {
                    var storey = building.Storeys[child.Storey.HasValue ? child.Storey.Value : 0];
                    var zone = building.Zones.FirstOrDefault(x => x.LinkId == child.ParentZoneId);
                    rows.Add(new ConstructionRow(storey, parent, zone, child, calculateUValueIfZero));
                }
            }

            rows.Sort();
            return rows;
        }

        public List<ConstructionRow> GenerateConstructionRowsFromElements(
            AssessmentComplianceBuildingDto building, List<OpeningTemplateDto> parents, bool calculateUValueIfZero = false)
        {
            var rows = new List<ConstructionRow>();
            foreach (var parent in parents)
            {
                foreach (var child in parent.Elements)
                {
                    var storey = building.Storeys[child.Storey.HasValue ? child.Storey.Value : 0];
                    var zone = building.Zones.FirstOrDefault(x => x.LinkId == child.ParentZoneId);
                    rows.Add(new ConstructionRow(storey, parent, zone, child));
                }
            }

            rows.Sort();
            return rows;
        }

        public List<ConstructionRow> CalculateOpaqueExteriorRows(AssessmentComplianceBuildingDto building)
        {

            var parents = building.Surfaces.Where(x =>
                {
                    var c = x.Category.ConstructionCategoryCode;
                    return ConstructionRow.OpaqueExteriorCategores.Contains(c);
                }).ToList();

            parents = parents.Where(x => x.ShowInReport).ToList();

            return GenerateConstructionRowsFromElements(building, parents, true);

        }

        public List<ConstructionRow> CalculateOpaqueInteriorRows(AssessmentComplianceBuildingDto building)
        {
            var parents = building.Surfaces.Where(x =>
            {
                var c = x.Category.ConstructionCategoryCode;
                return ConstructionRow.OpaqueInteriorCategories.Contains(c);

            }).ToList();

            parents = parents.Where(x => x.ShowInReport).ToList();

            return GenerateConstructionRowsFromElements(building, parents, true);
        }

        public List<ConstructionRow> CalculateExteriorFenestrationRows(AssessmentComplianceBuildingDto building)
        {
            var parents = building.Openings
                .Where(x => x.Category.ConstructionCategoryCode == "ExteriorGlazing")
                .ToList();

            parents = parents.Where(x => x.ShowInReport).ToList();

            var rows = GenerateConstructionRowsFromElements(building, parents);


            // Glass U-Factor Weighted Average = U-Factor(W1) x Area (W1) + U-Factor(W2) x Area (W2) + U-Factor(W3) x Area (W3) + U-Factor(W4) x Area (W4)
            // ------------divided by ------------------
            // Area (Total)
            //
            // Where W(1), W(2), W(3), W(4) etc. are all the applicable windows
            var totalGrossArea = rows.Count() == 0 ? 0 : rows.Select(x => x.GrossArea).Aggregate((a, b) => a + b);
            var totalOrAverageRow = new ConstructionRow()
            {
                Title = "Total or Average",
                Construction = "",
                Reflectance = null,

                GrossArea = totalGrossArea,

                // These are both 'area-weighted averages'
                SystemUValue = rows.Count() == 0 ? 0 : rows.Select(x => x.SystemUValue * x.GrossArea).Aggregate((a, b) => a + b) / totalGrossArea,
                SHGC = rows.Count() == 0 ? 0 : rows.Select(x => x.SHGC * x.GrossArea).Aggregate((a, b) => a + b) / totalGrossArea,

                Azimuth = null,
                Direction = "",
            };

            var northConstructions = rows.Where(x => x.Direction == "N").ToList();
            var northArea = northConstructions.Select(x => x.GrossArea).Aggregate(0m, (a, b) => a + b);

            var northRow = new ConstructionRow()
            {
                Title = "North Total or Average",
                Construction = "",
                Reflectance = null,

                GrossArea = northArea,

                // These are both 'area-weighted averages'
                SystemUValue = northConstructions.Count != 0
                    ? (northConstructions.Select(x => x.SystemUValue * x.GrossArea).Aggregate((a, b) => a + b) / northArea)
                    : 0,
                SHGC = northConstructions.Count != 0
                    ? (northConstructions.Select(x => x.SHGC * x.GrossArea).Aggregate((a, b) => a + b) / northArea)
                    : 0,

                Azimuth = null,
                Direction = "",
            };

            var nonNorthConstructions = rows.Where(x => x.Direction != "N").ToList();
            var nonNorthArea = nonNorthConstructions.Select(x => x.GrossArea).Aggregate(0m, (a, b) => a + b);
            var nonNorthRow = new ConstructionRow()
            {
                Title = "Non-North Total or Average",
                Construction = "",
                Reflectance = null,

                GrossArea = nonNorthArea,

                // These are both 'area-weighted averages'
                SystemUValue = nonNorthConstructions.Count != 0
                    ? (nonNorthConstructions.Select(x => x.SystemUValue * x.GrossArea).Aggregate((a, b) => a + b) / nonNorthArea)
                    : 0,
                SHGC =  nonNorthConstructions.Count != 0
                    ? (nonNorthConstructions.Select(x => x.SHGC * x.GrossArea).Aggregate((a, b) => a + b) / nonNorthArea)
                    : 0,

                Azimuth = null,
                Direction = "",
            };

            // Total or Average	blank	blank	calculated	Area weighted avg.	Area weighted avg.	blank	blank
            // North Total or Average	blank	blank	calculated	Area weighted avg.	Area weighted avg.	blank	blank
            // Non-North Total or Average	Blank	blank	calculated	Area weighted avg.	Area weighted avg.	blank	blank

            rows.Add(totalOrAverageRow);
            rows.Add(northRow);
            rows.Add(nonNorthRow);

            return rows;
        }

        public List<ConstructionRow> CalculateExteriorDoorRows(AssessmentComplianceBuildingDto building)
        {
            var parents = building.Surfaces
                .Where(x => x.Category.ConstructionCategoryCode == "ExteriorDoor")
                .ToList();

            parents = parents.Where(x => x.ShowInReport).ToList();

            return GenerateConstructionRowsFromElements(building, parents);
        }

        public List<ConstructionRow> CalculateInteriorDoorRows(AssessmentComplianceBuildingDto building)
        {
            var parents = building.Surfaces
                .Where(x => x.Category.ConstructionCategoryCode == "InteriorDoor")
                .ToList();

            parents = parents.Where(x => x.ShowInReport).ToList();

            return GenerateConstructionRowsFromElements(building, parents);
        }


        public string Round2DP(decimal? input)
        {
            if (input != null)
                return string.Format("{0:F2}", input);
            else
                return "";
        }

        public class ZoneSummaryRow
        {
            public string Title { get; set; }
            public string Conditioned { get; set; }
            public string PartOfTotalFloorArea { get; set; }
            public decimal FloorArea { get; set; }
            public decimal Volume { get; set; }
            public decimal GrossWallArea { get; set; }
            public decimal WindowArea { get; set; }

            public int Floor { get; set; }
        }

        public class XYRatioRow
        {
            public string Title { get; set; }
            public decimal Total { get; set; }
            public decimal North { get; set; }
            public decimal East { get; set; }
            public decimal South { get; set; }
            public decimal West { get; set; }
        }

        public class ConstructionRow : IComparable<ConstructionRow>
        {
            public string Title { get; set; }
            public string Construction { get; set; }
            public decimal? Reflectance { get; set; }
            public decimal SystemUValue { get; set; }
            public decimal SHGC { get; set; }
            public decimal GrossArea { get; set; }
            public decimal NetArea { get; set; }
            public decimal? Azimuth { get; set; }
            public string Direction { get; set; }

            public int Storey { get; set; }
            public string ZoneNumber { get; set; }
            public string CategoryCode { get; set; }
            public string ElementNumber { get; set; }

            // public ConstructionTemplateDto ConstructionRef { get; set; }


            // Opaque Interior
            //      Exterior Floor (Suspended) = Suspended Floor
            //      Intermediate Floor = Intermediate Floor
            //     Ceiling (Roof/Roof Space Above) = Ceiling
            //     Interior Wall (Partition) = Interior Wall Partition
            //     Interior Wall (Adjacent to Roof Space) = Interior Wall Roof Space
            //      Interior Wall (Adjacent to Subfloor Space) = Interior Wall Subfloor

            public static HashSet<string> OpaqueExteriorCategores = new HashSet<string>()
            {
                { "GroundFloor" },
                { "ExteriorFloorElevated" },
                { "ExteriorWall" },
                { "SubfloorWall" },
                { "Roof" },
            };

            public static HashSet<string> OpaqueInteriorCategories = new HashSet<string>()
            {
                { "ExteriorFloor" },
                // { "IntermediateFloorNeighbourBelow" }, -- NOT SURE?
                { "IntermediateFloor" },
                { "InteriorWall" },
                { "InteriorWallAdjacentToSubfloorSpace" },
                { "InteriorWallAdjacentToRoofSpace" },
                // { "CeilingNeighbourAbove" }, -- NOT SURE?
                { "CeilingRoofAbove" },
            };

            public static HashSet<string> ExteriorFenestrationCategories = new HashSet<string>()
            {
                { "ExteriorGlazing" },
            };

            // Ground Floor -> Elevated Floor –> Exterior Walls -> Subfloor Walls -> Roof
            public static Dictionary<string, int> SortOrder = new Dictionary<string, int>()
            {
                // Opaque Exterior
                { "GroundFloor", 0 },
                { "ExteriorFloorElevated", 1 },
                { "ExteriorWall", 2 },
                { "SubfloorWall", 3 },
                { "Roof", 4 },

                // Opaque Interior
                { "ExteriorFloor", 0 },
                // { "IntermediateFloorNeighbourBelow", 0  },
                { "IntermediateFloor", 1  },
                { "InteriorWall", 2  },
                { "InteriorWallAdjacentToSubfloorSpace", 3  },
                { "InteriorWallAdjacentToRoofSpace", 4  },
                // { "CeilingNeighbourAbove", 5  },
                { "CeilingRoofAbove", 6  },

                // Not grouped with any others.
                { "ExteriorGlazing", 0 },
                { "ExteriorDoor", 0 },
                { "InteriorDoor", 0 }
            };

            public ConstructionRow() { }

            public ConstructionRow(StoreyDto storey, SurfaceTemplateDto parent, ZoneDto zone, SurfaceTemplateDto child, bool calculateUValueIfZero = false)
            {
                var elemNum = child.ElementNumber.Split('-')[1];
                var categoryTitle = this.TransformCategoryTitle(child.Category);

                Title = $"{storey.Name}:{zone?.ZoneDescription}_{categoryTitle}_{elemNum}".ToUpper();
                Construction = FinalDescription(parent).ToUpper();
                Reflectance =  parent.ExteriorSolarAbsorptance.HasValue
                    ? 1 - parent.ExteriorSolarAbsorptance.Value
                    : parent.InteriorSolarAbsorptance.HasValue
                        ? 1 - parent.InteriorSolarAbsorptance
                        : 1;
                SystemUValue = parent.SystemRValue.HasValue && parent.SystemRValue.Value > 0
                    ?  1 / parent.SystemRValue.Value
                    :  calculateUValueIfZero
                        ? parent.InsulationData.UFactor
                        : 0;

                GrossArea = child.GrossArea.HasValue ? child.GrossArea.Value : 0;
                NetArea = child.NetArea.HasValue ? child.NetArea.Value : 0;
                Azimuth = child.Azimuth;
                Direction = AzimuthToNeswString(child.Azimuth, child.Category.ConstructionCategoryCode);

                // For sorting only...
                Storey = storey.Floor;
                ZoneNumber = zone?.ZoneNumber;
                CategoryCode = child.Category.ConstructionCategoryCode;
                ElementNumber = child.ElementNumber.Substring(3);
            }

            public ConstructionRow(StoreyDto storey, OpeningTemplateDto parent, ZoneDto zone, OpeningTemplateDto child)
            {
                var elemNum = child.ElementNumber.Split('-')[1];
                var categoryTitle = this.TransformCategoryTitle(child.Category);

                Title = $"{storey.Name}:{zone?.ZoneDescription}_{categoryTitle}_{elemNum}".ToUpper();
                Construction = FinalDescription(parent).ToUpper();
                Reflectance =  parent.FrameSolarAbsorptance.HasValue ? 1 - parent.FrameSolarAbsorptance.Value : 1;
                SystemUValue = FinalUValue(parent).HasValue ? FinalUValue(parent).Value : 0;
                SHGC = FinalSHGC(parent).HasValue ? FinalSHGC(parent).Value : 0;
                GrossArea = child.GrossArea.HasValue ? child.GrossArea.Value : 0;
                NetArea = child.NetArea.HasValue ? child.NetArea.Value : 0;
                Azimuth = child.Azimuth;
                Direction = AzimuthToNeswString(child.Azimuth, child.Category.ConstructionCategoryCode);

                // For sorting only...
                Storey = storey.Floor;
                ZoneNumber = zone?.ZoneNumber;
                CategoryCode = child.Category.ConstructionCategoryCode;
                ElementNumber = child.ElementNumber.Substring(3);
            }

            // -1 = they are bigger
            // 0 = equal
            // 1 = we are bigger.
            public int CompareTo(ConstructionRow them)
            {
                int ourScore = 0;
                int theirScore = 0;

                ourScore += (this.Storey + 1) * 1000000;
                theirScore += (them.Storey + 1) * 1000000;

                ourScore += Convert.ToInt32(this.ZoneNumber?.Substring(1) + "000");
                theirScore += Convert.ToInt32(them.ZoneNumber?.Substring(1) + "000");

                ourScore += SortOrder[this.CategoryCode] * 50;
                theirScore += SortOrder[them.CategoryCode] * 50;

                ourScore += Convert.ToInt32(this.ElementNumber);
                theirScore += Convert.ToInt32(them.ElementNumber);

                if (ourScore > theirScore)
                    return 1;
                else if (ourScore < theirScore)
                    return -1;
                else
                    return 0;
            }

            public string ToString() => $"{Title}";

            private string TransformCategoryTitle(ConstructionCategoryDto category)
            {
                string code = category.ConstructionCategoryCode;

                if (code == "GroundFloor")
                    return "Ground Floor";
                else if (code == "ExteriorFloorElevated")
                    return "Elevated Floor";
                else if (code == "ExteriorFloor")
                    return "Suspended Floor";
                else if (code == "CeilingRoofAbove")
                    return "Ceiling";
                else if (code == "InteriorWall")
                    return "Partition";
                else if (code == "InteriorWallAdjacentToRoofSpace")
                    return "Attic Partition";
                else if (code == "InteriorWallAdjacentToSubfloorSpace")
                    return "Subfloor Partition";
                else
                    return category.Title;

            }

            public static string FinalDescription(ConstructionTemplateDto parent)
            {
                return parent.OverrideDisplayDescription ??
                       parent.DisplayDescription ??
                       parent.Description;
            }

            // Final U-Value - Use override if it exists, otherwise fall back to original
            public static decimal? FinalUValue(OpeningTemplateDto opening)
            {
                return opening.Performance?.OverrideUValue ?? opening.Performance?.UValue;
            }

            // Final SHGC - Use override if it exists, otherwise fall back to original
            public static decimal? FinalSHGC(OpeningTemplateDto opening)
            {
                return opening.Performance?.OverrideSHGC ?? opening.Performance?.SHGC;
            }
        }

        private static string AzimuthToNeswString(decimal? azimuth, string categoryCode = null)
        {
            // When the category is floors or ceilings, the Azimuth is blank and the Cardinal Direction is blank
            if (categoryCode != null && (
                categoryCode == "GroundFloor" ||
                categoryCode == "ExteriorFloor" ||
                categoryCode == "ExteriorFloorElevated" ||
                categoryCode == "IntermediateFloor" ||
                categoryCode == "IntermediateFloorNeighbourBelow" ||
                categoryCode == "CeilingRoofAbove" ||
                categoryCode == "CeilingNeighbourAbove"))
            {
                return "";
            }

            // If the Azimuth is blank in scratch file, the Azimuth is blank and the Cardinal Direction is blank on the report
            if (azimuth.HasValue == false)
                return "";

            // If the Azimuth is 0, then the Azimuth is 0.00 and the Cardinal Direction is N
            if (azimuth == 0)
                return "N";

            if ((azimuth >= 315 && azimuth <= 360 || azimuth > 0 && azimuth < 45))
                return "N";
            if (azimuth >= 45 && azimuth < 135) return "E";
            if (azimuth >= 135 && azimuth < 225) return "S";
            if(azimuth >= 225 && azimuth < 315) return "W";

            return "????";
        }


    }

    public enum BuildingType
    {
        Proposed,
        Reference
    }

}