<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <!-- Keep these both here. The first is used while debugging in a browser window -->
    <link rel="stylesheet" href="template-styles.css">
    <link rel="stylesheet" href="$assessmentDetails.StyleSheetLocation">
</head>

<!-- NVelocity Values -->
#set($defaultIfNull = "Not Applicable")

<body style="width: 800px;">

<!-- Specification Summary -->
<div>

    <h1>$summaryDetails.SpecificationSummaryTitle</h1>

    <p nvel-show="$summaryDetails.IsPreliminaryReport">
        A summary of the required specifications is provided below. Not all specifications and compliance requirements
        are presented in this summary. The Energy Efficiency Compliance Report will provide additional compliance
        requirements and detailed specifications.
    </p>

    <p nvel-show="$summaryDetails.IsBaselineSpecification">
        A summary of the baseline specifications used in the assessment is provided below. Not all specifications and
        modelling assumptions used in the assessment are presented in this summary.
    </p>

    <p nvel-show="$summaryDetails.IsOptionSpecification">
        A summary of the required specifications is provided below. Not all specifications and compliance requirements
        are presented in this summary. The Energy Efficiency Compliance Report will provide additional compliance
        requirements and detailed specifications.
    </p>

    <!-- Construction -->
    #if($assessmentDetails.BuildingHasSurfaces($summaryDetails.ProposedBuilding))
    <div class="specification-summary-section">
        <h2>Construction</h2>
        <hr/>
        <table class="specification-summary-table">
            <tbody>
            #foreach($category in $assessmentDetails.SummaryConstructionCategories())
            <!--                    %foreach($category in @assessmentDetails.ConstructionTabCategories())-->
                #if($assessmentDetails.ConstructionsGroupedForCategoryHasItems($summaryDetails.ProposedBuilding, $category))
                <tr>
                    <td>
                        $assessmentDetails.FormattedSummaryCategoryTitle($category.Title, $category.ConstructionCategoryCode)
                    </td>
                    <td>
                        #foreach($construction in $assessmentDetails.ConstructionsGroupedForCategory($summaryDetails.ProposedBuilding, $category))
                        <div>
                            <span>$assessmentDetails.FinalSummaryDescriptionWithExtraDetails($construction)</span>
                       </div>
                        #end
                    </td>
                </tr>
                #end
            #end
            </tbody>
        </table>
    </div>
    #end

    <!-- Openings -->
    #if($assessmentDetails.BuildingHasOpenings($summaryDetails.ProposedBuilding))
    <div  class="specification-summary-section">
        <h2>Openings</h2>
        <hr/>
        <table class="specification-summary-table">
            <tbody>
            #foreach($category in $assessmentDetails.SummaryOpeningsCategories())

                #if($assessmentDetails.CategoryHasItems($summaryDetails.ProposedBuilding, $category))
                <tr>
                    <td>
                        $assessmentDetails.FormattedCategoryTitle($category.Title, $category.ConstructionCategoryCode)
                    </td>
                    <td>
                        #foreach($construction in $assessmentDetails.ConstructionsGroupedForCategory($summaryDetails.ProposedBuilding, $category))
                        <div>
                            <span>$assessmentDetails.FinalDescription($construction)</span>#if($assessmentDetails.IsAnOpening($category) == true)<span>, U = $assessmentDetails.Round2DP($assessmentDetails.FinalUValue($construction)) &amp; SHGC = $assessmentDetails.Round2DP($assessmentDetails.FinalSHGC($construction))</span>#end
                        </div>
                        #end
                    </td>
                </tr>
                #end
            #end
            </tbody>
        </table>
    </div>
    #end

    <!-- Services -->
    #if($assessmentDetails.BuildingHasServices($summaryDetails.ProposedBuilding))
    <div  class="specification-summary-section">
        <h2>Services</h2>
        <hr/>
        <table class="specification-summary-table">
            <tbody>
            #foreach($category in $assessmentDetails.SummaryServicesCategories())

                #if($assessmentDetails.ServiceCategoryHasItems($summaryDetails.ProposedBuilding, $category))
                <tr>
                    <td>
                        $assessmentDetails.FormattedCategoryTitle($category.Title, $category.ConstructionCategoryCode)
                    </td>
                    <td>
                        #foreach($construction in $assessmentDetails.ServicesForCategory($summaryDetails.ProposedBuilding, $category))
                        <div>
                            <span>$assessmentDetails.FinalDescription($construction)</span>#if($assessmentDetails.IsAnOpening($category) == true)<span>, U = $assessmentDetails.Round2DP($assessmentDetails.FinalUValue($construction)) &amp; SHGC = $assessmentDetails.Round2DP($assessmentDetails.FinalSHGC($construction))</span>#end
                        </div>
                        #end
                        <!-- Cooking -->
                        #if($category.ServiceCategoryCode == 'Cooking')
                            <span>$category.Description</span>
                        #end
                    </td>
                </tr>
                #end
            #end
            </tbody>
        </table>
    </div>
    #end

    <!-- "Other" -->
    <div class="specification-summary-section">
        <h2>Other</h2>
        <hr/>
        <table class="specification-summary-table">
            <tbody>
            <tr>
                <td>Thermal Insulation</td>
                <td>
                    Building fabric thermal insulation must comply with $assessmentDetails.N1322Or31211().
                </td>
            </tr>
            #if($summaryDetails.IsElementalProvision)
            <tr>
                <td>Ceiling Fans</td>
                <td>
                    #if($summaryDetails.IsNcc2022Certification())
                    Ceiling fans must be installed in accordance with 13.5.2.
                    #else
                    Ceiling fans must be installed in accordance with ********.
                    #end
                </td>
            </tr>
            #end
            <tr>
                <td>Exhaust Fans</td>
                <td>
                    Exhaust fans must be fitted with a sealing device.
                </td>
            </tr>
            <tr>
                <td>Recessed Light Fittings</td>
                <td>
                    Recessed light fittings must be sealed and IC rated in accordance with AS/NZS 60598.2.2:2016.
                </td>
            </tr>
            #if($assessmentDetails.CategoryHasServices($assessmentDetails.ProposedBuilding, 'CeilingFans'))
                <tr>
                    <td>Ceiling Fans</td>
                    #foreach($service in $assessmentDetails.ServicesInCategory($assessmentDetails.ProposedBuilding, 'CeilingFans'))
                        <td>$assessmentDetails.FinalDisplayDescription($service), $assessmentDetails.BladeDiameter($service)mm blade diameter, permanently installed with a speed controller.</td>
                    #end
                </tr>
            #end

            </tbody>
        </table>
    </div>
</div>

</body>

</html>
